name: Build
on: [ pull_request, push, workflow_dispatch ]

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        minecraft: [ 1.16.5, 1.17.1, 1.18.2, 1.19.2, 1.19.4, 1.20.1, 1.20.4 ]
    runs-on: ubuntu-latest
    env:
      BUILD_ARTIFACTS: ${{ secrets.BUILD_ARTIFACTS }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@main
      - name: Validate gradle wrapper
        uses: gradle/wrapper-validation-action@main
      - name: Setup JDK 17
        uses: actions/setup-java@main
        with:
          java-version: 17
          distribution: 'zulu'
      - name: Make gradle wrapper executable
        run: chmod +x ./gradlew
      - name: Setup website files
        run: ./gradlew fabric:setupWebsiteFiles
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Node packages
        run: npm ci --prefix buildSrc/src/main/resources/website
      - name: Build Angular
        run: npm run build --prefix buildSrc/src/main/resources/website
      - name: Setup Fabric files
        if: ${{ matrix.minecraft != '1.20.4' }}
        run: ./gradlew fabric:setupFiles -PminecraftVersion="${{ matrix.minecraft }}" -PcrowdinApiKey="${{ secrets.CROWDIN_API_KEY }}" -PpatreonApiKey="${{ secrets.PATREON_API_KEY }}"
      - name: Setup Fabric files
        if: ${{ matrix.minecraft == '1.20.4' }}
        run: ./gradlew fabric:setupFiles -PminecraftVersion="${{ matrix.minecraft }}" -PcrowdinApiKey="${{ secrets.CROWDIN_API_KEY }}" -PpatreonApiKey="${{ secrets.PATREON_API_KEY }}" -PgeminiApiKey="${{ secrets.GEMINI_API_KEY }}"
      - name: Setup Forge files
        run: ./gradlew forge:setupFiles -PminecraftVersion="${{ matrix.minecraft }}"
      - name: Build ${{ matrix.minecraft }}
        run: ./gradlew build -PminecraftVersion="${{ matrix.minecraft }}"
      - name: Build ${{ matrix.minecraft }} (server)
        run: ./gradlew build -PminecraftVersion="${{ matrix.minecraft }}" -PexcludeAssets=true
      - name: Capture release artifacts
        if: env.BUILD_ARTIFACTS == 'true'
        uses: actions/upload-artifact@main
        with:
          name: Fabric and Forge ${{ matrix.minecraft }}
          path: build/release/
      - name: Post Crowdin translation analysis
        if: ${{ matrix.minecraft == '1.20.4' }}
        run: cat build/translation/analysis.md >> $GITHUB_STEP_SUMMARY
        continue-on-error: true
