name: Crowdin
on: [ workflow_dispatch ]

jobs:
  crowdin:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@main
      - name: Upload Translations to Crowdin
        env:
          MY_KEY: ${{ secrets.CROWDIN_API_KEY }}
        if: ${{ env.MY_KEY != '' }}
        uses: crowdin/github-action@master
        with:
          upload_sources: true
          upload_translations: false
          download_translations: false
          push_translations: false
          create_pull_request: false
          project_id: '455212'
          token: ${{ secrets.CROWDIN_API_KEY }}
          config: 'crowdin.yml'
