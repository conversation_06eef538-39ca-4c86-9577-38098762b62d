repositories {
	mavenCentral()
}

dependencies {
	implementation "com.google.code.gson:gson:+"
	implementation "com.github.crowdin:crowdin-api-client-java:+"
	implementation "it.unimi.dsi:fastutil:+"
	implementation "commons-io:commons-io:+"
	implementation "org.apache.httpcomponents:httpmime:+"
	implementation "org.mtr:Minecraft-Mod-API-Tools:0.0.1"
	implementation "org.mtr:Minecraft-Mappings-common:0.0.1"
	implementation "org.mtr:Transport-Simulation-Core:Build-Tools"
}

repositories {
	flatDir { dirs "../libs" }
	maven { url "https://jitpack.io" }
}

java {
	toolchain {
		languageVersion.set(JavaLanguageVersion.of(8))
	}
}
