{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"chatAnnouncements": {"type": "boolean", "default": true}, "textToSpeechAnnouncements": {"type": "boolean", "default": true}, "hideTranslucentParts": {"type": "boolean"}, "languageDisplay": {"$ref": "LanguageDisplay"}, "vehicleOscillationMultiplier": {"type": "number", "default": 1.0}, "dynamicTextureResolution": {"type": "integer", "default": 2}, "defaultRail3D": {"type": "boolean", "default": true}, "useMTRFont": {"type": "boolean"}, "disableShadowsForShaders": {"type": "boolean"}, "preloadResourcePattern": {"type": "string"}, "betaWarningVersion": {"type": "string"}}}