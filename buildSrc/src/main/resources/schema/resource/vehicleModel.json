{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "javaConstructorFields": ["resourceProvider"], "properties": {"modelResource": {"type": "string"}, "textureResource": {"type": "string"}, "modelPropertiesResource": {"type": "string"}, "positionDefinitionsResource": {"type": "string"}, "flipTextureV": {"type": "boolean"}}, "required": ["modelResource", "textureResource", "modelPropertiesResource", "positionDefinitionsResource", "flipTextureV"]}