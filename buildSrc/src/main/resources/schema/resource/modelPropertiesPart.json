{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"names": {"type": "array", "items": {"type": "string"}}, "positionDefinitions": {"type": "array", "items": {"type": "string"}}, "condition": {"$ref": "PartCondition", "typeScriptEnum": "NORMAL|AT_DEPOT|ON_ROUTE_FORWARDS|ON_ROUTE_BACKWARDS|DOORS_CLOSED|DOORS_OPENED|CHRISTMAS_LIGHT_RED|CHRISTMAS_LIGHT_YELLOW|CHRISTMAS_LIGHT_GREEN|CHRISTMAS_LIGHT_BLUE"}, "renderStage": {"$ref": "RenderStage", "typeScriptEnum": "LIGHT|ALWAYS_ON_LIGHT|INTERIOR|INTERIOR_TRANSLUCENT|EXTERIOR"}, "type": {"$ref": "PartType", "typeScriptEnum": "NORMAL|DISPLAY|FLOOR|DOORWAY|SEAT"}, "displayXPadding": {"type": "number", "minimum": 0}, "displayYPadding": {"type": "number", "minimum": 0}, "displayColorCjk": {"type": "string"}, "displayColor": {"type": "string"}, "displayMaxLineHeight": {"type": "number", "minimum": 0}, "displayCjkSizeRatio": {"type": "number", "minimum": 0}, "displayOptions": {"type": "array", "items": {"type": "string"}}, "displayPadZeros": {"type": "number", "minimum": 0}, "displayType": {"$ref": "DisplayType", "typeScriptEnum": "DESTINATION|ROUTE_NUMBER|DEPARTURE_INDEX|NEXT_STATION|NEXT_STATION_KCR|NEXT_STATION_MTR|NEXT_STATION_UK|ROUTE_COLOR|ROUTE_COLOR_ROUNDED"}, "displayDefaultText": {"type": "string"}, "doorXMultiplier": {"type": "number"}, "doorZMultiplier": {"type": "number"}, "doorAnimationType": {"$ref": "DoorAnimationType", "typeScriptEnum": "STANDARD|STANDARD_SLOW|CONSTANT|PLUG_FAST|PLUG_SLOW|PLUG_SLOW_2|PLUG_SLOW_3|BOUNCY_1|BOUNCY_2|MLR|R179|R211"}, "renderFromOpeningDoorTime": {"type": "integer", "minimum": 0}, "renderUntilOpeningDoorTime": {"type": "integer", "minimum": 0}, "renderFromClosingDoorTime": {"type": "integer", "minimum": 0}, "renderUntilClosingDoorTime": {"type": "integer", "minimum": 0}, "flashOffTime": {"type": "integer", "minimum": 0}, "flashOnTime": {"type": "integer", "minimum": 0}}, "required": ["condition", "renderStage", "type", "displayXPadding", "displayYPadding", "displayColorCjk", "displayColor", "displayMaxLineHeight", "displayCjkSizeRatio", "displayPadZeros", "displayType", "displayDefaultText", "doorXMultiplier", "doorZMultiplier", "doorAnimationType", "renderFromOpeningDoorTime", "renderUntilOpeningDoorTime", "renderFromClosingDoorTime", "renderUntilClosingDoorTime", "flashOffTime", "flashOnTime"]}