{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "javaConstructorFields": ["resourceProvider"], "properties": {"vehicles": {"type": "array", "items": {"$ref": "vehicleResource.json", "parameters": ["ResourceProvider"]}}, "signs": {"type": "array", "items": {"$ref": "signResource.json"}}, "rails": {"type": "array", "items": {"$ref": "railResource.json", "parameters": ["ResourceProvider"]}}, "objects": {"type": "array", "items": {"$ref": "objectResource.json", "parameters": ["ResourceProvider"]}}, "lifts": {"type": "array", "items": {"$ref": "liftResource.json"}}}}