{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"parts": {"type": "array", "items": {"$ref": "modelPropertiesPart.json"}}, "modelYOffset": {"type": "number"}, "gangwayInnerSideResource": {"type": "string"}, "gangwayInnerTopResource": {"type": "string"}, "gangwayInnerBottomResource": {"type": "string"}, "gangwayOuterSideResource": {"type": "string"}, "gangwayOuterTopResource": {"type": "string"}, "gangwayOuterBottomResource": {"type": "string"}, "gangwayWidth": {"type": "number", "minimum": 0}, "gangwayHeight": {"type": "number", "minimum": 0}, "gangwayYOffset": {"type": "number"}, "gangwayZOffset": {"type": "number"}, "barrierInnerSideResource": {"type": "string"}, "barrierInnerTopResource": {"type": "string"}, "barrierInnerBottomResource": {"type": "string"}, "barrierOuterSideResource": {"type": "string"}, "barrierOuterTopResource": {"type": "string"}, "barrierOuterBottomResource": {"type": "string"}, "barrierWidth": {"type": "number", "minimum": 0}, "barrierHeight": {"type": "number", "minimum": 0}, "barrierYOffset": {"type": "number"}, "barrierZOffset": {"type": "number"}}, "required": ["modelYOffset", "gangwayInnerSideResource", "gangwayInnerTopResource", "gangwayInnerBottomResource", "gangwayOuterSideResource", "gangwayOuterTopResource", "gangwayOuterBottomResource", "gangwayWidth", "gangwayHeight", "gangwayYOffset", "gangwayZOffset", "barrierInnerSideResource", "barrierInnerTopResource", "barrierInnerBottomResource", "barrierOuterSideResource", "barrierOuterTopResource", "barrierOuterBottomResource", "barrierWidth", "barrierHeight", "barrierYOffset", "barrierZOffset"]}