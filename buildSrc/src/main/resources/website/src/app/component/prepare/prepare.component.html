<div class="column gap wrapper">
	<div class="column center wrapper">
		<div class="spacing"></div>
		@if (hasData()) {
			<app-large-tile [clickable]="true" icon="undo" (clicked)="openResetDialog()">
				<h2>Reset Resource Pack Creator</h2>
				<div>This will clear all progress and unsaved changes.</div>
			</app-large-tile>
		} @else {
			<div class="row gap center">
				<app-large-tile [clickable]="true" icon="add" [disabled]="!!uploader.getFileNames()" (clicked)="create()">
					<h2>Create New Resource Pack</h2>
					<div>The sky's the limit!</div>
				</app-large-tile>
				<mat-divider class="divider" vertical/>
				<app-large-tile [clickable]="true" icon="edit" [disabled]="!!uploader.getFileNames()" (clicked)="uploader.upload()">
					<h2>Edit Existing Resource Pack</h2>
					<div>Make it your own!</div>
				</app-large-tile>
			</div>
		}
		<div class="spacing"></div>
		<div class="uploader">
			<app-uploader #uploader [fileExtensions]="['zip']" [allowMultiple]="false" endpoint="upload/zip" (uploaded)="upload()"/>
		</div>
	</div>
</div>
